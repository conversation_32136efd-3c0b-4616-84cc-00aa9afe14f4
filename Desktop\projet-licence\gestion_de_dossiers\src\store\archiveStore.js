import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useArchiveStore = create(
  persist(
    (set, get) => ({
      // État des archives
      archives: [],
      
      // Archiver un dossier automatiquement
      archiveDossier: (dossier, facture = null) => {
        const archivedDossier = {
          ...dossier,
          archivedAt: new Date().toISOString(),
          archiveReason: facture ? 'Dossier terminé et facturé' : 'Dossier terminé',
          originalStatus: dossier.status,
          facture: facture ? {
            id: facture.id,
            numero: facture.numero,
            total: facture.total,
            datePaiement: facture.datePaiement
          } : null
        };
        
        set((state) => ({
          archives: [...state.archives, archivedDossier]
        }));
        
        return archivedDossier;
      },
      
      // Rechercher dans les archives
      searchArchives: (query) => {
        const { archives } = get();
        const searchTerm = query.toLowerCase();
        
        return archives.filter(archive =>
          archive.numero.toLowerCase().includes(searchTerm) ||
          archive.clientName.toLowerCase().includes(searchTerm) ||
          archive.description.toLowerCase().includes(searchTerm)
        );
      },
      
      // Filtrer par période
      getArchivesByPeriod: (startDate, endDate) => {
        const { archives } = get();
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        return archives.filter(archive => {
          const archiveDate = new Date(archive.archivedAt);
          return archiveDate >= start && archiveDate <= end;
        });
      },
      
      // Filtrer par type
      getArchivesByType: (type) => {
        const { archives } = get();
        return archives.filter(archive => archive.type === type);
      },
      
      // Statistiques des archives
      getStats: () => {
        const { archives } = get();
        
        const total = archives.length;
        const byType = {
          export: archives.filter(a => a.type === 'export').length,
          transit: archives.filter(a => a.type === 'transit').length,
          transport: archives.filter(a => a.type === 'transport').length
        };
        
        const withBilling = archives.filter(a => a.facture !== null).length;
        const totalRevenue = archives
          .filter(a => a.facture !== null)
          .reduce((sum, a) => sum + a.facture.total, 0);
        
        // Archives par mois (12 derniers mois)
        const monthlyStats = [];
        for (let i = 11; i >= 0; i--) {
          const date = new Date();
          date.setMonth(date.getMonth() - i);
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          
          const monthArchives = archives.filter(archive => {
            const archiveDate = new Date(archive.archivedAt);
            return archiveDate.getFullYear() === date.getFullYear() && 
                   archiveDate.getMonth() === date.getMonth();
          });
          
          monthlyStats.push({
            month: monthKey,
            count: monthArchives.length,
            revenue: monthArchives
              .filter(a => a.facture !== null)
              .reduce((sum, a) => sum + a.facture.total, 0)
          });
        }
        
        return {
          total,
          byType,
          withBilling,
          totalRevenue,
          monthlyStats
        };
      },
      
      // Restaurer un dossier depuis les archives (si nécessaire)
      restoreDossier: (archiveId) => {
        const { archives } = get();
        const archive = archives.find(a => a.id === archiveId);
        
        if (archive) {
          // Retirer des archives
          set((state) => ({
            archives: state.archives.filter(a => a.id !== archiveId)
          }));
          
          // Retourner le dossier restauré (sans les métadonnées d'archive)
          const { archivedAt, archiveReason, originalStatus, ...restoredDossier } = archive;
          return {
            ...restoredDossier,
            status: 'en_cours', // Remettre en cours
            updatedAt: new Date().toISOString()
          };
        }
        
        return null;
      },
      
      // Nettoyage automatique (archives > 5 ans)
      cleanOldArchives: () => {
        const fiveYearsAgo = new Date();
        fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5);
        
        set((state) => ({
          archives: state.archives.filter(archive => {
            const archiveDate = new Date(archive.archivedAt);
            return archiveDate > fiveYearsAgo;
          })
        }));
      }
    }),
    {
      name: 'archive-store'
    }
  )
);

export default useArchiveStore;
