import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const BILLING_STATUS = {
  DRAFT: 'brouillon',
  SENT: 'envoyee',
  PAID: 'payee',
  OVERDUE: 'en_retard'
};

// Tarifs par défaut selon le type de workflow
export const DEFAULT_RATES = {
  export: {
    base: 150000, // 150k FCFA
    perStep: 5000
  },
  transit: {
    base: 200000, // 200k FCFA
    perStep: 8000
  },
  transport: {
    base: 75000, // 75k FCFA
    perStep: 3000
  }
};

const useBillingStore = create(
  persist(
    (set, get) => ({
      // État des factures
      factures: [],
      
      // Actions CRUD
      createFacture: (dossier, clientInfo) => {
        const rates = DEFAULT_RATES[dossier.type] || DEFAULT_RATES.transport;
        const completedSteps = dossier.steps?.filter(s => s.completed).length || 0;
        
        const montantBase = rates.base;
        const montantEtapes = completedSteps * rates.perStep;
        const sousTotal = montantBase + montantEtapes;
        const tva = sousTotal * 0.1925; // TVA 19.25%
        const total = sousTotal + tva;
        
        const newFacture = {
          id: Date.now().toString(),
          numero: generateFactureNumber(),
          dossierId: dossier.id,
          clientId: dossier.clientId,
          clientName: clientInfo.name,
          
          // Détails facturation
          lignes: [
            {
              description: `Dossier ${dossier.type.toUpperCase()} - ${dossier.numero}`,
              quantite: 1,
              prixUnitaire: montantBase,
              total: montantBase
            },
            {
              description: `Étapes complétées (${completedSteps})`,
              quantite: completedSteps,
              prixUnitaire: rates.perStep,
              total: montantEtapes
            }
          ],
          
          // Totaux
          sousTotal,
          tva,
          total,
          
          // Statut et dates
          status: BILLING_STATUS.DRAFT,
          dateCreation: new Date().toISOString(),
          dateEcheance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // +30 jours
          datePaiement: null,
          
          // Métadonnées
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        set((state) => ({
          factures: [...state.factures, newFacture]
        }));
        
        return newFacture;
      },
      
      updateFactureStatus: (factureId, status, datePaiement = null) => {
        set((state) => ({
          factures: state.factures.map(facture =>
            facture.id === factureId
              ? { 
                  ...facture, 
                  status, 
                  datePaiement,
                  updatedAt: new Date().toISOString()
                }
              : facture
          )
        }));
      },
      
      getFacturesByClient: (clientId) => {
        const { factures } = get();
        return factures.filter(facture => facture.clientId === clientId);
      },
      
      getFacturesByStatus: (status) => {
        const { factures } = get();
        return factures.filter(facture => facture.status === status);
      },
      
      getFactureByDossier: (dossierId) => {
        const { factures } = get();
        return factures.find(facture => facture.dossierId === dossierId);
      },
      
      // Statistiques simples
      getStats: () => {
        const { factures } = get();
        
        const totalFactures = factures.length;
        const facturesPayees = factures.filter(f => f.status === BILLING_STATUS.PAID).length;
        const chiffreAffaires = factures
          .filter(f => f.status === BILLING_STATUS.PAID)
          .reduce((sum, f) => sum + f.total, 0);
        const enAttente = factures.filter(f => f.status === BILLING_STATUS.SENT).length;
        
        return {
          totalFactures,
          facturesPayees,
          chiffreAffaires,
          enAttente,
          tauxPaiement: totalFactures > 0 ? (facturesPayees / totalFactures) * 100 : 0
        };
      },
      
      // Auto-archivage des dossiers payés
      checkAutoArchive: (dossierId) => {
        const facture = get().getFactureByDossier(dossierId);
        return facture && facture.status === BILLING_STATUS.PAID;
      }
    }),
    {
      name: 'billing-store'
    }
  )
);

// Fonction utilitaire pour générer un numéro de facture
const generateFactureNumber = () => {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');
  const timestamp = Date.now().toString().slice(-4);
  return `FACT-${year}${month}-${timestamp}`;
};

export default useBillingStore;
