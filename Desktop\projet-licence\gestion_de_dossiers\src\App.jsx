import { useState } from 'react'
import { Outlet, useLocation } from 'react-router-dom'
import Header from './components/layout/Header'
import Sidebar from './components/layout/Sidebar'

function App() {
  const location = useLocation()
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  // Structure de navigation
  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    { path: '/files', label: 'Dossiers', icon: 'fas fa-folder' },
    { path: '/clients', label: 'Clients', icon: 'fas fa-users' },
    { path: '/transit', label: 'Transit', icon: 'fas fa-truck' },
    { path: '/transport', label: 'Transport', icon: 'fas fa-shipping-fast' },
    { path: '/billing', label: 'Facturation', icon: 'fas fa-file-invoice-dollar' },
    { path: '/archive', label: 'Archives', icon: 'fas fa-archive' },
    { path: '/settings', label: 'Paramètres', icon: 'fas fa-cog' },
  ];

  return (
    <div className="app-layout">
      <Header
        onMenuClick={() => setIsSidebarOpen(true)}
      />
      <div className="app-body flex">
        <Sidebar
          isOpen={isSidebarOpen}
          onClose={() => setIsSidebarOpen(false)}
          navItems={navItems}
          currentPath={location.pathname}
        />
        <main className="app-main flex-1">
          <Outlet />
        </main>
      </div>
    </div>
  )
}

export default App