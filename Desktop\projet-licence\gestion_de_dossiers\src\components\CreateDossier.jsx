import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import useDossierStore, { WORKFLOW_TYPES } from '../store/dossierStore';
import useStore from '../store';

const CreateDossier = ({ onClose, onSuccess }) => {
  const { register, handleSubmit, watch, formState: { errors } } = useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { createDossier } = useDossierStore();
  const { currentUser } = useStore();
  
  const selectedType = watch('type');

  const onSubmit = async (data) => {
    setIsLoading(true);
    try {
      const dossierData = {
        ...data,
        assignedTo: currentUser.id,
        assignedBy: currentUser.id,
        priority: data.priority || 'normale'
      };
      
      const newDossier = createDossier(dossierData);
      toast.success(`Dossier ${newDossier.numero} créé avec succès !`);
      
      if (onSuccess) {
        onSuccess(newDossier);
      }
      
      if (onClose) {
        onClose();
      }
    } catch (error) {
      toast.error('Erreur lors de la création du dossier');
    } finally {
      setIsLoading(false);
    }
  };

  const workflowDescriptions = {
    [WORKFLOW_TYPES.EXPORT]: 'Gestion complète des exportations avec 27 étapes incluant douanes, certifications et transport.',
    [WORKFLOW_TYPES.TRANSIT]: 'Procédures de transit douanier avec 27 étapes spécialisées pour les opérations de transit.',
    [WORKFLOW_TYPES.TRANSPORT]: 'Workflow simplifié de transport avec 10 étapes pour les livraisons directes.'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <i className="fas fa-plus text-white"></i>
              </div>
              <h2 className="text-xl font-semibold text-white">Créer un nouveau dossier</h2>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors p-2 rounded-lg hover:bg-white hover:bg-opacity-10"
            >
              <i className="fas fa-times text-lg"></i>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(95vh-80px)]">
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
            {/* Type de workflow */}
            <div className="form-group">
              <label className="form-label">
                Type de dossier *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.values(WORKFLOW_TYPES).map((type) => (
                  <label key={type} className="relative group">
                    <input
                      type="radio"
                      value={type}
                      {...register('type', { required: 'Le type de dossier est requis' })}
                      className="sr-only"
                    />
                    <div className={`p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 group-hover:shadow-md ${
                      selectedType === type
                        ? 'border-blue-500 bg-blue-50 shadow-md ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                    }`}>
                      <div className="text-center">
                        <div className="text-3xl mb-3">
                          {type === WORKFLOW_TYPES.EXPORT && '📦'}
                          {type === WORKFLOW_TYPES.TRANSIT && '🚛'}
                          {type === WORKFLOW_TYPES.TRANSPORT && '🚚'}
                        </div>
                        <div className="font-semibold text-gray-900 capitalize mb-1">{type}</div>
                        <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full inline-block">
                          {type === WORKFLOW_TYPES.EXPORT && '27 étapes'}
                          {type === WORKFLOW_TYPES.TRANSIT && '27 étapes'}
                          {type === WORKFLOW_TYPES.TRANSPORT && '10 étapes'}
                        </div>
                        {selectedType === type && (
                          <div className="mt-2">
                            <i className="fas fa-check-circle text-blue-500"></i>
                          </div>
                        )}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
              {selectedType && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <i className="fas fa-info-circle text-blue-500 mt-0.5"></i>
                    <p className="text-sm text-blue-700">
                      {workflowDescriptions[selectedType]}
                    </p>
                  </div>
                </div>
              )}
              {errors.type && (
                <p className="form-error">{errors.type.message}</p>
              )}
            </div>

            {/* Informations client */}
            <div className="bg-gray-50 p-6 rounded-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i className="fas fa-user-tie mr-2 text-blue-600"></i>
                Informations client
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label className="form-label">
                    Nom du client *
                  </label>
                  <input
                    type="text"
                    {...register('clientName', { required: 'Le nom du client est requis' })}
                    className="form-input"
                    placeholder="Nom de l'entreprise"
                  />
                  {errors.clientName && (
                    <p className="form-error">{errors.clientName.message}</p>
                  )}
                </div>

                <div className="form-group">
                  <label className="form-label">
                    Contact client
                  </label>
                  <input
                    type="text"
                    {...register('clientContact')}
                    className="form-input"
                    placeholder="Nom du contact"
                  />
                </div>
              </div>
            </div>

            {/* Description et priorité */}
            <div className="bg-gray-50 p-6 rounded-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i className="fas fa-file-alt mr-2 text-blue-600"></i>
                Détails du dossier
              </h3>

              <div className="form-group">
                <label className="form-label">
                  Description *
                </label>
                <textarea
                  {...register('description', { required: 'La description est requise' })}
                  rows={4}
                  className="form-textarea"
                  placeholder="Description détaillée du dossier, marchandises, instructions spéciales..."
                />
                {errors.description && (
                  <p className="form-error">{errors.description.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label className="form-label">
                    Priorité
                  </label>
                  <select
                    {...register('priority')}
                    className="form-select"
                  >
                    <option value="basse">🟢 Basse</option>
                    <option value="normale" selected>🟡 Normale</option>
                    <option value="haute">🟠 Haute</option>
                    <option value="urgente">🔴 Urgente</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">
                    Date limite
                  </label>
                  <input
                    type="date"
                    {...register('deadline')}
                    className="form-input"
                    min={new Date().toISOString().split('T')[0]}
                  />
                  <p className="form-help">Optionnel - Date limite pour la finalisation du dossier</p>
                </div>
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="btn btn-outline"
                disabled={isLoading}
              >
                <i className="fas fa-times mr-2"></i>
                Annuler
              </button>
              <button
                type="submit"
                disabled={isLoading || !selectedType}
                className="btn btn-primary btn-lg"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="loading-spinner w-4 h-4 mr-2"></div>
                    Création en cours...
                  </div>
                ) : (
                  <>
                    <i className="fas fa-plus mr-2"></i>
                    Créer le dossier
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateDossier;
