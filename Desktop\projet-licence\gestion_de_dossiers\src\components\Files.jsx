import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import useStore from '../store'
import useDossierStore from '../store/dossierStore'
import CreateDossier from './CreateDossier'

const Files = () => {
  const navigate = useNavigate()
  const { searchQuery } = useStore()
  const { dossiers, setCurrentDossier } = useDossierStore()
  const [selectedDossier, setSelectedDossier] = useState(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [statusFilter, setStatusFilter] = useState('all')

  // Filtrage des dossiers
  const filteredDossiers = dossiers.filter(dossier => {
    const matchesSearch =
      dossier.numero.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dossier.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dossier.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || dossier.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleCreateSuccess = (newDossier) => {
    setCurrentDossier(newDossier);
    setSelectedDossier(newDossier);
  };

  const getStatusBadgeClass = (status) => {
    const statusClasses = {
      'brouillon': 'bg-gray-100 text-gray-800',
      'en_cours': 'bg-blue-100 text-blue-800',
      'termine': 'bg-green-100 text-green-800',
      'annule': 'bg-red-100 text-red-800',
      'archive': 'bg-gray-100 text-gray-600'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  return (
    <div className="files">
      <div className="files-header">
        <div className="header-content">
          <h1>Dossiers</h1>
          <p>Gérez vos dossiers d'import/export</p>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateModal(true)}
          >
            <i className="fas fa-plus"></i>
            Nouveau dossier
          </button>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tous les statuts</option>
            <option value="brouillon">Brouillon</option>
            <option value="en_cours">En cours</option>
            <option value="termine">Terminé</option>
            <option value="annule">Annulé</option>
            <option value="archive">Archivé</option>
          </select>
        </div>
      </div>

      <div className="files-content">
        <div className="files-list">
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>Numéro</th>
                  <th>Client</th>
                  <th>Type</th>
                  <th>Statut</th>
                  <th>Progression</th>
                  <th>Date création</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredDossiers.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="text-center py-8 text-gray-500">
                      {dossiers.length === 0
                        ? "Aucun dossier créé. Cliquez sur 'Nouveau dossier' pour commencer."
                        : "Aucun dossier ne correspond aux critères de recherche."
                      }
                    </td>
                  </tr>
                ) : (
                  filteredDossiers.map(dossier => (
                    <tr
                      key={dossier.id}
                      className={selectedDossier?.id === dossier.id ? 'selected' : ''}
                      onClick={() => setSelectedDossier(dossier)}
                    >
                      <td className="font-medium">{dossier.numero}</td>
                      <td>{dossier.clientName}</td>
                      <td className="capitalize">{dossier.type}</td>
                      <td>
                        <span className={`status-badge ${getStatusBadgeClass(dossier.status)}`}>
                          {dossier.status.replace('_', ' ')}
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${dossier.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600">{dossier.progress}%</span>
                        </div>
                      </td>
                      <td>{formatDate(dossier.createdAt)}</td>
                      <td>
                        <div className="table-actions">
                          <button
                            className="btn btn-icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCurrentDossier(dossier);
                              navigate(`/files/${dossier.id}`);
                            }}
                            title="Voir les détails"
                          >
                            <i className="fas fa-eye"></i>
                          </button>
                          <button
                            className="btn btn-icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Édition du dossier (à implémenter)
                            }}
                          >
                            <i className="fas fa-edit"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {selectedDossier && (
          <div className="file-details">
            <div className="card">
              <div className="card-header">
                <h2>Détails du dossier</h2>
                <div className="card-actions">
                  <button
                    className="btn btn-icon"
                    onClick={() => {
                      setCurrentDossier(selectedDossier);
                      // Navigation vers la page de détail
                    }}
                  >
                    <i className="fas fa-external-link-alt"></i>
                  </button>
                  <button
                    className="btn btn-icon"
                    onClick={() => setSelectedDossier(null)}
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <div className="card-body space-y-4">
                <div className="detail-item">
                  <label>Numéro du dossier</label>
                  <div className="font-medium">{selectedDossier.numero}</div>
                </div>
                <div className="detail-item">
                  <label>Client</label>
                  <div>{selectedDossier.clientName}</div>
                </div>
                <div className="detail-item">
                  <label>Contact</label>
                  <div>{selectedDossier.clientContact || 'Non renseigné'}</div>
                </div>
                <div className="detail-item">
                  <label>Type de workflow</label>
                  <div className="capitalize">{selectedDossier.type}</div>
                </div>
                <div className="detail-item">
                  <label>Statut</label>
                  <div>
                    <span className={`status-badge ${getStatusBadgeClass(selectedDossier.status)}`}>
                      {selectedDossier.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
                <div className="detail-item">
                  <label>Progression</label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>{selectedDossier.progress}% complété</span>
                      <span className="text-sm text-gray-500">
                        {selectedDossier.steps?.filter(s => s.completed).length || 0} / {selectedDossier.steps?.length || 0} étapes
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${selectedDossier.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                <div className="detail-item">
                  <label>Description</label>
                  <div className="text-sm text-gray-600">{selectedDossier.description}</div>
                </div>
                <div className="detail-item">
                  <label>Date de création</label>
                  <div>{formatDate(selectedDossier.createdAt)}</div>
                </div>
                {selectedDossier.deadline && (
                  <div className="detail-item">
                    <label>Date limite</label>
                    <div>{formatDate(selectedDossier.deadline)}</div>
                  </div>
                )}
                <div className="detail-item">
                  <label>Priorité</label>
                  <div className="capitalize">{selectedDossier.priority}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modal de création de dossier */}
      {showCreateModal && (
        <CreateDossier
          onClose={() => setShowCreateModal(false)}
          onSuccess={handleCreateSuccess}
        />
      )}
    </div>
  )
}

export default Files