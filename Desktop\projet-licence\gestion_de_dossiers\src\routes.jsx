import { createBrowserRouter } from "react-router-dom";
import App from "./App";
import Dashboard from "./components/Dashboard";
import Files from "./components/Files";
import Clients from "./components/Clients";
import Transit from "./components/Transit";
import Transport from "./components/Transport";
import Billing from "./components/Billing";
import Archive from "./components/Archive";
import Settings from "./components/Settings";
import Login from "./components/Login";
import ProtectedRoute from "./components/ProtectedRoute";

export const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "/",
        element: <Dashboard />,
      },
      {
        path: "/dashboard",
        element: <Dashboard />,
      },
      {
        path: "/files",
        element: <Files />,
      },
      {
        path: "/clients",
        element: <Clients />,
      },
      {
        path: "/transit",
        element: <Transit />,
      },
      {
        path: "/transport",
        element: <Transport />,
      },
      {
        path: "/billing",
        element: <Billing />,
      },
      {
        path: "/archive",
        element: <Archive />,
      },
      {
        path: "/settings",
        element: <Settings />,
      },
      {
        path: "/login",
        element: <Login />,
      },
    ],
  },
]);