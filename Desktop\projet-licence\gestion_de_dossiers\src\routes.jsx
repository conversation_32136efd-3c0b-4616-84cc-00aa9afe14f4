import { createBrowserRouter } from "react-router-dom";
import App from "./App";
import Dashboard from "./components/Dashboard";
import Files from "./components/Files";
import DossierDetailPage from "./components/DossierDetailPage";
import Clients from "./components/Clients";
import Transit from "./components/Transit";
import Transport from "./components/Transport";
import Billing from "./components/Billing";
import Archive from "./components/Archive";
import Settings from "./components/Settings";
import Login from "./components/Login";
import ProtectedRoute from "./components/ProtectedRoute";

export const router = createBrowserRouter([
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "/",
    element: (
      <ProtectedRoute>
        <App />
      </ProtectedRoute>
    ),
    children: [
      {
        path: "/",
        element: <Dashboard />,
      },
      {
        path: "/dashboard",
        element: <Dashboard />,
      },
      {
        path: "/files",
        element: (
          <ProtectedRoute requiredPermission="create_dossier">
            <Files />
          </ProtectedRoute>
        ),
      },
      {
        path: "/files/:dossierId",
        element: (
          <ProtectedRoute requiredPermission="view_assigned">
            <DossierDetailPage />
          </ProtectedRoute>
        ),
      },
      {
        path: "/clients",
        element: (
          <ProtectedRoute requiredPermission="view_all">
            <Clients />
          </ProtectedRoute>
        ),
      },
      {
        path: "/transit",
        element: (
          <ProtectedRoute requiredPermission="create_dossier">
            <Transit />
          </ProtectedRoute>
        ),
      },
      {
        path: "/transport",
        element: (
          <ProtectedRoute requiredPermission="create_dossier">
            <Transport />
          </ProtectedRoute>
        ),
      },
      {
        path: "/billing",
        element: (
          <ProtectedRoute requiredPermission="billing">
            <Billing />
          </ProtectedRoute>
        ),
      },
      {
        path: "/archive",
        element: (
          <ProtectedRoute requiredPermission="view_all">
            <Archive />
          </ProtectedRoute>
        ),
      },
      {
        path: "/settings",
        element: (
          <ProtectedRoute requiredPermission="admin">
            <Settings />
          </ProtectedRoute>
        ),
      },
    ],
  },
]);