import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const CLIENT_TYPES = {
  IMPORTER: 'importateur',
  EXPORTER: 'exportateur',
  TRANSPORTER: 'transporteur',
  MIXED: 'mixte'
};

export const CLIENT_STATUS = {
  ACTIVE: 'actif',
  INACTIVE: 'inactif',
  SUSPENDED: 'suspendu'
};

const useClientStore = create(
  persist(
    (set, get) => ({
      // État des clients
      clients: [
        // Clients de démonstration
        {
          id: '1',
          name: 'Global Imports SARL',
          type: CLIENT_TYPES.IMPORTER,
          status: CLIENT_STATUS.ACTIVE,
          contact: '<PERSON>',
          email: '<EMAIL>',
          phone: '+237 6 99 88 77 66',
          address: '123 Rue du Commerce, Douala',
          siret: '12345678901234',
          createdAt: '2024-01-15T10:00:00.000Z',
          lastActivity: '2024-02-20T14:30:00.000Z'
        },
        {
          id: '2',
          name: 'Export Plus SA',
          type: CLIENT_TYPES.EXPORTER,
          status: CLIENT_STATUS.ACTIVE,
          contact: '<PERSON>',
          email: '<EMAIL>',
          phone: '+237 6 88 77 66 55',
          address: '456 Avenue de l\'Indépendance, Yaoundé',
          siret: '98765432109876',
          createdAt: '2024-01-20T09:15:00.000Z',
          lastActivity: '2024-02-19T16:45:00.000Z'
        }
      ],
      
      // Actions CRUD
      createClient: (clientData) => {
        const newClient = {
          id: Date.now().toString(),
          ...clientData,
          status: CLIENT_STATUS.ACTIVE,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        };
        
        set((state) => ({
          clients: [...state.clients, newClient]
        }));
        
        return newClient;
      },
      
      updateClient: (clientId, updates) => {
        set((state) => ({
          clients: state.clients.map(client =>
            client.id === clientId
              ? { 
                  ...client, 
                  ...updates,
                  lastActivity: new Date().toISOString()
                }
              : client
          )
        }));
      },
      
      deleteClient: (clientId) => {
        set((state) => ({
          clients: state.clients.filter(client => client.id !== clientId)
        }));
      },
      
      getClientById: (clientId) => {
        const { clients } = get();
        return clients.find(client => client.id === clientId);
      },
      
      getActiveClients: () => {
        const { clients } = get();
        return clients.filter(client => client.status === CLIENT_STATUS.ACTIVE);
      },
      
      getClientsByType: (type) => {
        const { clients } = get();
        return clients.filter(client => client.type === type);
      },
      
      searchClients: (query) => {
        const { clients } = get();
        const searchTerm = query.toLowerCase();
        
        return clients.filter(client =>
          client.name.toLowerCase().includes(searchTerm) ||
          client.contact.toLowerCase().includes(searchTerm) ||
          client.email.toLowerCase().includes(searchTerm)
        );
      },
      
      // Statistiques simples
      getStats: () => {
        const { clients } = get();
        
        const total = clients.length;
        const actifs = clients.filter(c => c.status === CLIENT_STATUS.ACTIVE).length;
        const inactifs = clients.filter(c => c.status === CLIENT_STATUS.INACTIVE).length;
        
        const byType = {
          importateurs: clients.filter(c => c.type === CLIENT_TYPES.IMPORTER).length,
          exportateurs: clients.filter(c => c.type === CLIENT_TYPES.EXPORTER).length,
          transporteurs: clients.filter(c => c.type === CLIENT_TYPES.TRANSPORTER).length,
          mixtes: clients.filter(c => c.type === CLIENT_TYPES.MIXED).length
        };
        
        return {
          total,
          actifs,
          inactifs,
          byType
        };
      },
      
      // Mise à jour de la dernière activité
      updateLastActivity: (clientId) => {
        set((state) => ({
          clients: state.clients.map(client =>
            client.id === clientId
              ? { ...client, lastActivity: new Date().toISOString() }
              : client
          )
        }));
      }
    }),
    {
      name: 'client-store'
    }
  )
);

export default useClientStore;
