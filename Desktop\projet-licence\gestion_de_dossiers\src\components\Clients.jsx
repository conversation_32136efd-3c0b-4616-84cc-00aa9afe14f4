import { useState } from 'react'
import useStore from '../store'

const Clients = () => {
  const { searchQuery } = useStore()
  const [clients] = useState([
    {
      id: 1,
      name: 'Global Imports Ltd',
      type: 'Importateur',
      status: 'Actif',
      contact: '<PERSON>',
      email: '<EMAIL>',
      phone: '+33 1 23 45 67 89',
      address: '123 Rue de la Paix, 75001 Paris',
      siret: '12345678901234',
      dossiers: 15,
      lastActivity: '2024-02-20'
    },
    {
      id: 2,
      name: 'Tech Solutions Inc',
      type: 'Exportateur',
      status: 'Actif',
      contact: '<PERSON>',
      email: '<EMAIL>',
      phone: '+33 1 98 76 54 32',
      address: '456 Avenue des Champs, 75008 Paris',
      siret: '98765432109876',
      dossiers: 8,
      lastActivity: '2024-02-19'
    },
    {
      id: 3,
      name: 'Logistics Pro SARL',
      type: 'Transporteur',
      status: 'Inactif',
      contact: '<PERSON>',
      email: '<EMAIL>',
      phone: '+33 1 11 22 33 44',
      address: '789 Boulevard du Port, 13002 Marseille',
      siret: '11223344556677',
      dossiers: 3,
      lastActivity: '2024-01-15'
    },
    {
      id: 4,
      name: 'Trade Masters SA',
      type: 'Importateur',
      status: 'Actif',
      contact: 'Sarah Wilson',
      email: '<EMAIL>',
      phone: '+33 1 55 66 77 88',
      address: '321 Quai de la Loire, 69002 Lyon',
      siret: '55667788990011',
      dossiers: 22,
      lastActivity: '2024-02-21'
    }
  ])

  const [selectedClient, setSelectedClient] = useState(null)
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')

  // Filtrage des clients
  const filteredClients = clients.filter(client => {
    const matchesSearch =
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.contact.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === 'all' || client.status.toLowerCase() === statusFilter
    const matchesType = typeFilter === 'all' || client.type.toLowerCase() === typeFilter.toLowerCase()

    return matchesSearch && matchesStatus && matchesType
  })

  return (
    <div className="page-container">
      {/* Header */}
      <div className="section-header">
        <div>
          <h1 className="section-title">Gestion des Clients</h1>
          <p className="section-subtitle">
            Gérez votre portefeuille client et leurs informations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">Tous les statuts</option>
            <option value="actif">Actif</option>
            <option value="inactif">Inactif</option>
          </select>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">Tous les types</option>
            <option value="importateur">Importateur</option>
            <option value="exportateur">Exportateur</option>
            <option value="transporteur">Transporteur</option>
          </select>
          <button className="btn btn-primary">
            <i className="fas fa-plus mr-2"></i>
            Nouveau client
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="content-grid content-grid-4 mb-8">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100 mr-4">
                <i className="fas fa-users text-blue-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total clients</p>
                <p className="text-2xl font-bold text-gray-900">{filteredClients.length}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100 mr-4">
                <i className="fas fa-user-check text-green-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Clients actifs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredClients.filter(c => c.status === 'Actif').length}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-100 mr-4">
                <i className="fas fa-folder text-yellow-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Dossiers totaux</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredClients.reduce((sum, client) => sum + client.dossiers, 0)}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-100 mr-4">
                <i className="fas fa-chart-line text-purple-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Moyenne dossiers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredClients.length > 0 ? Math.round(filteredClients.reduce((sum, client) => sum + client.dossiers, 0) / filteredClients.length) : 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table des clients */}
      <div className="card card-elevated">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Liste des clients</h3>
          <div className="flex items-center space-x-2">
            <button className="btn btn-ghost btn-sm">
              <i className="fas fa-download mr-2"></i>
              Exporter
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>Client</th>
                <th>Type</th>
                <th>Statut</th>
                <th>Contact</th>
                <th>Dossiers</th>
                <th>Dernière activité</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredClients.length === 0 ? (
                <tr>
                  <td colSpan="7" className="text-center py-8">
                    <div className="text-gray-500">
                      <i className="fas fa-users text-4xl mb-4"></i>
                      <p>Aucun client trouvé</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredClients.map(client => (
                  <tr
                    key={client.id}
                    className={`table-row ${selectedClient?.id === client.id ? 'selected' : ''}`}
                    onClick={() => setSelectedClient(client)}
                  >
                    <td>
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-blue-600 font-medium text-sm">
                            {client.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{client.name}</p>
                          <p className="text-sm text-gray-500">{client.email}</p>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span className="badge badge-secondary">{client.type}</span>
                    </td>
                    <td>
                      <span className={`badge ${client.status === 'Actif' ? 'badge-success' : 'badge-danger'}`}>
                        {client.status}
                      </span>
                    </td>
                    <td>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{client.contact}</p>
                        <p className="text-sm text-gray-500">{client.phone}</p>
                      </div>
                    </td>
                    <td>
                      <span className="font-medium">{client.dossiers}</span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-500">
                        {new Date(client.lastActivity).toLocaleDateString('fr-FR')}
                      </span>
                    </td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <button className="btn-icon" title="Modifier">
                          <i className="fas fa-edit"></i>
                        </button>
                        <button className="btn-icon" title="Voir dossiers">
                          <i className="fas fa-folder"></i>
                        </button>
                        <button className="btn-icon" title="Supprimer">
                          <i className="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Panneau de détails */}
      {selectedClient && (
        <div className="mt-8">
          <div className="card card-elevated">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Détails du client</h3>
              <button
                className="btn btn-ghost btn-sm"
                onClick={() => setSelectedClient(null)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Nom de l'entreprise</label>
                  <p className="text-sm text-gray-900 font-medium">{selectedClient.name}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Type de client</label>
                  <span className="badge badge-secondary">{selectedClient.type}</span>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Statut</label>
                  <span className={`badge ${selectedClient.status === 'Actif' ? 'badge-success' : 'badge-danger'}`}>
                    {selectedClient.status}
                  </span>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Contact principal</label>
                  <p className="text-sm text-gray-900">{selectedClient.contact}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p className="text-sm text-gray-900">{selectedClient.email}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Téléphone</label>
                  <p className="text-sm text-gray-900">{selectedClient.phone}</p>
                </div>
                <div className="space-y-1 md:col-span-2">
                  <label className="text-sm font-medium text-gray-600">Adresse</label>
                  <p className="text-sm text-gray-900">{selectedClient.address}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">SIRET</label>
                  <p className="text-sm text-gray-900">{selectedClient.siret}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Nombre de dossiers</label>
                  <p className="text-sm text-gray-900 font-medium">{selectedClient.dossiers}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Dernière activité</label>
                  <p className="text-sm text-gray-900">{new Date(selectedClient.lastActivity).toLocaleDateString('fr-FR')}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <button className="btn btn-primary">
                    <i className="fas fa-edit mr-2"></i>
                    Modifier
                  </button>
                  <button className="btn btn-outline">
                    <i className="fas fa-folder mr-2"></i>
                    Voir les dossiers
                  </button>
                  <button className="btn btn-outline">
                    <i className="fas fa-file-invoice mr-2"></i>
                    Facturation
                  </button>
                  <button className="btn btn-danger">
                    <i className="fas fa-trash mr-2"></i>
                    Supprimer
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Clients