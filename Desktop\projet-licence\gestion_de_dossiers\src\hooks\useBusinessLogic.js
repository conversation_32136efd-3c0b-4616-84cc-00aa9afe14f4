import { useCallback } from 'react';
import { toast } from 'react-toastify';
import useDossierStore, { DOSSIER_STATUS } from '../store/dossierStore';
import useClientStore from '../store/clientStore';
import useBillingStore, { BILLING_STATUS } from '../store/billingStore';
import useArchiveStore from '../store/archiveStore';

/**
 * Hook personnalisé pour orchestrer la logique métier critique
 * Gère les interactions entre dossiers, clients, facturation et archivage
 */
const useBusinessLogic = () => {
  const { 
    createDossier, 
    updateDossier, 
    updateStepStatus, 
    getDossiersByStatus 
  } = useDossierStore();
  
  const { 
    getClientById, 
    updateLastActivity 
  } = useClientStore();
  
  const { 
    createFacture, 
    updateFactureStatus, 
    getFactureByDossier,
    checkAutoArchive 
  } = useBillingStore();
  
  const { archiveDossier } = useArchiveStore();

  /**
   * Créer un dossier complet avec validation client
   */
  const createCompleteDossier = useCallback(async (dossierData) => {
    try {
      // Vérifier que le client existe et est actif
      const client = getClientById(dossierData.clientId);
      if (!client) {
        throw new Error('Client introuvable');
      }
      if (client.status !== 'actif') {
        throw new Error('Le client n\'est pas actif');
      }

      // Créer le dossier
      const newDossier = createDossier({
        ...dossierData,
        clientName: client.name
      });

      // Mettre à jour l'activité du client
      updateLastActivity(client.id);

      toast.success(`Dossier ${newDossier.numero} créé avec succès`);
      return newDossier;

    } catch (error) {
      toast.error(`Erreur lors de la création: ${error.message}`);
      throw error;
    }
  }, [createDossier, getClientById, updateLastActivity]);

  /**
   * Compléter une étape avec vérifications automatiques
   */
  const completeStep = useCallback(async (dossierId, stepId, notes = '') => {
    try {
      // Marquer l'étape comme terminée
      updateStepStatus(dossierId, stepId, true, notes);

      // Récupérer le dossier mis à jour
      const dossier = useDossierStore.getState().dossiers.find(d => d.id === dossierId);
      
      if (!dossier) {
        throw new Error('Dossier introuvable');
      }

      // Vérifier si toutes les étapes sont terminées
      const allStepsCompleted = dossier.steps.every(step => step.completed);
      
      if (allStepsCompleted && dossier.status !== DOSSIER_STATUS.COMPLETED) {
        // Marquer le dossier comme terminé
        updateDossier(dossierId, { 
          status: DOSSIER_STATUS.COMPLETED,
          completedAt: new Date().toISOString()
        });

        // Déclencher la facturation automatique
        await triggerBilling(dossierId);
        
        toast.success('Dossier terminé ! Facturation générée automatiquement.');
      } else {
        toast.success('Étape marquée comme terminée');
      }

    } catch (error) {
      toast.error(`Erreur: ${error.message}`);
      throw error;
    }
  }, [updateStepStatus, updateDossier]);

  /**
   * Déclencher la facturation automatique
   */
  const triggerBilling = useCallback(async (dossierId) => {
    try {
      const dossier = useDossierStore.getState().dossiers.find(d => d.id === dossierId);
      const client = getClientById(dossier.clientId);

      if (!dossier || !client) {
        throw new Error('Dossier ou client introuvable');
      }

      // Vérifier qu'il n'y a pas déjà une facture
      const existingFacture = getFactureByDossier(dossierId);
      if (existingFacture) {
        toast.info('Une facture existe déjà pour ce dossier');
        return existingFacture;
      }

      // Créer la facture
      const facture = createFacture(dossier, client);
      
      toast.success(`Facture ${facture.numero} générée`);
      return facture;

    } catch (error) {
      toast.error(`Erreur lors de la facturation: ${error.message}`);
      throw error;
    }
  }, [getClientById, getFactureByDossier, createFacture]);

  /**
   * Marquer une facture comme payée et déclencher l'archivage
   */
  const markFacturePaid = useCallback(async (factureId, dossierId) => {
    try {
      // Marquer la facture comme payée
      updateFactureStatus(factureId, BILLING_STATUS.PAID, new Date().toISOString());

      // Récupérer le dossier et la facture
      const dossier = useDossierStore.getState().dossiers.find(d => d.id === dossierId);
      const facture = useBillingStore.getState().factures.find(f => f.id === factureId);

      if (dossier && facture) {
        // Archiver automatiquement
        await triggerArchiving(dossierId, factureId);
        toast.success('Facture payée et dossier archivé automatiquement');
      }

    } catch (error) {
      toast.error(`Erreur: ${error.message}`);
      throw error;
    }
  }, [updateFactureStatus]);

  /**
   * Déclencher l'archivage automatique
   */
  const triggerArchiving = useCallback(async (dossierId, factureId = null) => {
    try {
      const dossier = useDossierStore.getState().dossiers.find(d => d.id === dossierId);
      
      if (!dossier) {
        throw new Error('Dossier introuvable');
      }

      // Vérifier les conditions d'archivage
      if (dossier.status !== DOSSIER_STATUS.COMPLETED) {
        throw new Error('Le dossier doit être terminé pour être archivé');
      }

      let facture = null;
      if (factureId) {
        facture = useBillingStore.getState().factures.find(f => f.id === factureId);
      }

      // Archiver le dossier
      const archivedDossier = archiveDossier(dossier, facture);

      // Mettre à jour le statut du dossier
      updateDossier(dossierId, { status: DOSSIER_STATUS.ARCHIVED });

      toast.success(`Dossier ${dossier.numero} archivé avec succès`);
      return archivedDossier;

    } catch (error) {
      toast.error(`Erreur lors de l'archivage: ${error.message}`);
      throw error;
    }
  }, [archiveDossier, updateDossier]);

  /**
   * Obtenir le statut global d'un dossier
   */
  const getDossierStatus = useCallback((dossierId) => {
    const dossier = useDossierStore.getState().dossiers.find(d => d.id === dossierId);
    const facture = getFactureByDossier(dossierId);
    
    if (!dossier) return null;

    return {
      dossier: {
        status: dossier.status,
        progress: dossier.progress,
        completedSteps: dossier.steps?.filter(s => s.completed).length || 0,
        totalSteps: dossier.steps?.length || 0
      },
      billing: facture ? {
        status: facture.status,
        total: facture.total,
        dateEcheance: facture.dateEcheance
      } : null,
      canArchive: checkAutoArchive(dossierId)
    };
  }, [getFactureByDossier, checkAutoArchive]);

  /**
   * Statistiques globales du business
   */
  const getGlobalStats = useCallback(() => {
    const dossierStats = {
      total: useDossierStore.getState().dossiers.length,
      enCours: getDossiersByStatus(DOSSIER_STATUS.IN_PROGRESS).length,
      termines: getDossiersByStatus(DOSSIER_STATUS.COMPLETED).length,
      archives: getDossiersByStatus(DOSSIER_STATUS.ARCHIVED).length
    };

    const billingStats = useBillingStore.getState().getStats();
    const clientStats = useClientStore.getState().getStats();
    const archiveStats = useArchiveStore.getState().getStats();

    return {
      dossiers: dossierStats,
      billing: billingStats,
      clients: clientStats,
      archives: archiveStats
    };
  }, [getDossiersByStatus]);

  return {
    // Actions principales
    createCompleteDossier,
    completeStep,
    triggerBilling,
    markFacturePaid,
    triggerArchiving,
    
    // Utilitaires
    getDossierStatus,
    getGlobalStats
  };
};

export default useBusinessLogic;
