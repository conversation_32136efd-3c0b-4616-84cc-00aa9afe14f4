import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useStore = create(
  persist(
    (set, get) => ({
      // État d'authentification
      isAuthenticated: false,
      currentUser: null,
      userPermissions: [],

      // État global
      searchQuery: '',
      showNotifications: false,
  notifications: [
    {
      id: 1,
      type: 'info',
      title: 'Nouveau dossier',
      message: 'Un nouveau dossier a été créé',
      time: 'Il y a 5 minutes',
      read: false
    },
    {
      id: 2,
      type: 'warning',
      title: 'Document en retard',
      message: 'Le document #12345 est en retard',
      time: 'Il y a 1 heure',
      read: false
    },
    {
      id: 3,
      type: 'success',
      title: 'Facture payée',
      message: 'La facture #789 a été payée',
      time: 'Il y a 2 heures',
      read: true
    }
  ],

      // Actions d'authentification
      login: (user, token) => {
        const permissions = getUserPermissions(user.role);
        set({
          isAuthenticated: true,
          currentUser: user,
          userPermissions: permissions
        });
        localStorage.setItem('authToken', token);
      },

      logout: () => {
        set({
          isAuthenticated: false,
          currentUser: null,
          userPermissions: []
        });
        localStorage.removeItem('authToken');
      },

      updateUser: (userData) => set((state) => ({
        currentUser: { ...state.currentUser, ...userData }
      })),

      // Actions générales
      setSearchQuery: (query) => set({ searchQuery: query }),
      setShowNotifications: (show) => set({ showNotifications: show }),
      addNotification: (notification) =>
        set((state) => ({
          notifications: [notification, ...state.notifications]
        })),
      markNotificationAsRead: (id) =>
        set((state) => ({
          notifications: state.notifications.map((n) =>
            n.id === id ? { ...n, read: true } : n
          )
        })),
      clearNotifications: () => set({ notifications: [] }),

      // Vérification des permissions
      hasPermission: (permission) => {
        const { userPermissions } = get();
        return userPermissions.includes(permission) || userPermissions.includes('admin');
      }
    }),
    {
      name: 'logistics-store',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        currentUser: state.currentUser,
        userPermissions: state.userPermissions
      })
    }
  )
)

// Définition des permissions par rôle
const getUserPermissions = (role) => {
  const permissions = {
    'admin': ['admin', 'create_dossier', 'edit_dossier', 'delete_dossier', 'view_all', 'manage_users', 'billing', 'reports'],
    'responsable': ['create_dossier', 'edit_dossier', 'view_all', 'billing', 'reports'],
    'agent': ['create_dossier', 'edit_dossier', 'view_assigned'],
    'chauffeur': ['view_assigned', 'update_status'],
    'client': ['view_own', 'track_shipment']
  };

  return permissions[role] || [];
};

export default useStore