import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import useStore from "../store";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const { login } = useStore();

  // Utilisateurs de démonstration
  const demoUsers = {
    '<EMAIL>': {
      password: 'admin123',
      user: {
        id: 1,
        name: 'Administrateur',
        email: '<EMAIL>',
        role: 'admin',
        avatar: 'AD'
      }
    },
    '<EMAIL>': {
      password: 'resp123',
      user: {
        id: 2,
        name: 'Responsable Logistique',
        email: '<EMAIL>',
        role: 'responsable',
        avatar: 'RL'
      }
    },
    '<EMAIL>': {
      password: 'agent123',
      user: {
        id: 3,
        name: 'Agent Transit',
        email: '<EMAIL>',
        role: 'agent',
        avatar: 'AT'
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Validation basique
      if (!email || !password) {
        setError("Veuillez remplir tous les champs.");
        return;
      }

      // Simulation d'une authentification
      await new Promise(resolve => setTimeout(resolve, 1000));

      const userCredentials = demoUsers[email];

      if (!userCredentials || userCredentials.password !== password) {
        setError("Email ou mot de passe incorrect.");
        return;
      }

      // Connexion réussie
      const token = 'demo-jwt-token-' + Date.now();
      login(userCredentials.user, token);

      toast.success(`Bienvenue ${userCredentials.user.name} !`);
      navigate('/dashboard');

    } catch (err) {
      setError("Une erreur est survenue lors de la connexion.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg w-96">
        <div className="text-center mb-6">
          <div className="text-3xl mb-2">🚛</div>
          <h2 className="text-2xl font-bold text-gray-800">Logistics Pro</h2>
          <p className="text-gray-600">Gestion de dossiers transport</p>
        </div>

        <form onSubmit={handleSubmit}>
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              disabled={isLoading}
              required
            />
          </div>

          <div className="mb-6">
            <label className="block mb-2 text-sm font-medium text-gray-700">
              Mot de passe
            </label>
            <input
              type="password"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="••••••••"
              disabled={isLoading}
              required
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Connexion...
              </div>
            ) : (
              'Se connecter'
            )}
          </button>
        </form>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Comptes de démonstration :</h3>
          <div className="text-xs text-gray-600 space-y-1">
            <div><strong>Admin:</strong> <EMAIL> / admin123</div>
            <div><strong>Responsable:</strong> <EMAIL> / resp123</div>
            <div><strong>Agent:</strong> <EMAIL> / agent123</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
