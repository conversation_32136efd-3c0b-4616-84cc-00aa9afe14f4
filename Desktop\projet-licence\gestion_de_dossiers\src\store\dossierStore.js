import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Définition des workflows selon le cahier des charges
export const WORKFLOW_TYPES = {
  EXPORT: 'export',
  TRANSIT: 'transit',
  TRANSPORT: 'transport'
};

// Workflows SIMPLIFIÉS - Fonctionnalités critiques uniquement
export const WORKFLOW_STEPS = {
  [WORKFLOW_TYPES.EXPORT]: [
    { id: 1, name: 'Ouverture de dossier', required: true, category: 'preparation' },
    { id: 2, name: 'Réception work order', required: true, category: 'preparation' },
    { id: 3, name: 'Validation documents', required: true, category: 'documentation' },
    { id: 4, name: 'Contrat transporteur', required: true, category: 'transport' },
    { id: 5, name: 'Chargement cargaison', required: true, category: 'execution' },
    { id: 6, name: 'Départ marchandise', required: true, category: 'execution' },
    { id: 7, name: 'Suivi transport', required: true, category: 'execution' },
    { id: 8, name: 'Livraison client', required: true, category: 'livraison' },
    { id: 9, name: 'Confirmation réception', required: true, category: 'finalisation' },
    { id: 10, name: 'Clôture dossier', required: true, category: 'finalisation' }
  ],
  
  [WORKFLOW_TYPES.TRANSIT]: [
    { id: 1, name: 'Ouverture de dossier', required: true, category: 'preparation' },
    { id: 2, name: 'Déclaration douane', required: true, category: 'douane' },
    { id: 3, name: 'Validation douane', required: true, category: 'douane' },
    { id: 4, name: 'Préparation transit', required: true, category: 'preparation' },
    { id: 5, name: 'Chargement cargaison', required: true, category: 'execution' },
    { id: 6, name: 'Transit frontière', required: true, category: 'execution' },
    { id: 7, name: 'Livraison destination', required: true, category: 'livraison' },
    { id: 8, name: 'Clôture dossier', required: true, category: 'finalisation' }
  ],
  
  [WORKFLOW_TYPES.TRANSPORT]: [
    { id: 1, name: 'Ouverture de dossier', required: true, category: 'preparation' },
    { id: 2, name: 'Préparation commande', required: true, category: 'preparation' },
    { id: 3, name: 'Chargement véhicule', required: true, category: 'execution' },
    { id: 4, name: 'Transport marchandise', required: true, category: 'execution' },
    { id: 5, name: 'Livraison client', required: true, category: 'livraison' },
    { id: 6, name: 'Clôture dossier', required: true, category: 'finalisation' }
  ]
};

export const DOSSIER_STATUS = {
  DRAFT: 'brouillon',
  IN_PROGRESS: 'en_cours',
  COMPLETED: 'termine',
  CANCELLED: 'annule',
  ARCHIVED: 'archive'
};

const useDossierStore = create(
  persist(
    (set, get) => ({
      // État des dossiers
      dossiers: [],
      currentDossier: null,
      
      // Actions CRUD
      createDossier: (dossierData) => {
        const newDossier = {
          id: Date.now().toString(),
          numero: generateDossierNumber(dossierData.type),
          ...dossierData,
          status: DOSSIER_STATUS.DRAFT,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          steps: initializeSteps(dossierData.type),
          progress: 0
        };
        
        set((state) => ({
          dossiers: [...state.dossiers, newDossier],
          currentDossier: newDossier
        }));
        
        return newDossier;
      },
      
      updateDossier: (dossierId, updates) => {
        set((state) => ({
          dossiers: state.dossiers.map(dossier =>
            dossier.id === dossierId
              ? { 
                  ...dossier, 
                  ...updates, 
                  updatedAt: new Date().toISOString(),
                  progress: calculateProgress(dossier.steps)
                }
              : dossier
          ),
          currentDossier: state.currentDossier?.id === dossierId
            ? { ...state.currentDossier, ...updates, updatedAt: new Date().toISOString() }
            : state.currentDossier
        }));
      },
      
      updateStepStatus: (dossierId, stepId, completed, notes = '') => {
        set((state) => ({
          dossiers: state.dossiers.map(dossier => {
            if (dossier.id === dossierId) {
              const updatedSteps = dossier.steps.map(step =>
                step.id === stepId
                  ? { 
                      ...step, 
                      completed, 
                      completedAt: completed ? new Date().toISOString() : null,
                      notes 
                    }
                  : step
              );
              
              const progress = calculateProgress(updatedSteps);
              const status = progress === 100 ? DOSSIER_STATUS.COMPLETED : DOSSIER_STATUS.IN_PROGRESS;
              
              return {
                ...dossier,
                steps: updatedSteps,
                progress,
                status,
                updatedAt: new Date().toISOString()
              };
            }
            return dossier;
          })
        }));
      },
      
      setCurrentDossier: (dossier) => set({ currentDossier: dossier }),
      
      getDossiersByStatus: (status) => {
        const { dossiers } = get();
        return dossiers.filter(dossier => dossier.status === status);
      },
      
      getDossiersByType: (type) => {
        const { dossiers } = get();
        return dossiers.filter(dossier => dossier.type === type);
      }
    }),
    {
      name: 'dossier-store'
    }
  )
);

// Fonctions utilitaires
const generateDossierNumber = (type) => {
  const prefix = type.toUpperCase().substring(0, 3);
  const timestamp = Date.now().toString().slice(-6);
  return `${prefix}-${timestamp}`;
};

const initializeSteps = (workflowType) => {
  const steps = WORKFLOW_STEPS[workflowType] || [];
  return steps.map(step => ({
    ...step,
    completed: false,
    completedAt: null,
    notes: ''
  }));
};

const calculateProgress = (steps) => {
  if (!steps || steps.length === 0) return 0;
  const completedSteps = steps.filter(step => step.completed).length;
  return Math.round((completedSteps / steps.length) * 100);
};

export default useDossierStore;
