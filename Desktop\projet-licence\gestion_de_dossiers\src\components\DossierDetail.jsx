import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import useDossierStore from '../store/dossierStore';
import useStore from '../store';

const DossierDetail = ({ dossierId, onClose }) => {
  const navigate = useNavigate();
  const { dossiers, updateStepStatus, updateDossier } = useDossierStore();
  const { hasPermission } = useStore();
  const [activeTab, setActiveTab] = useState('steps');
  
  const dossier = dossiers.find(d => d.id === dossierId);
  
  if (!dossier) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-4xl mb-4">📁</div>
          <h3 className="text-lg font-medium text-gray-900">Dossier non trouvé</h3>
          <p className="text-gray-500">Le dossier demandé n'existe pas.</p>
        </div>
      </div>
    );
  }

  const handleStepToggle = (stepId, completed) => {
    if (!hasPermission('edit_dossier')) {
      toast.error('Vous n\'avez pas les permissions pour modifier ce dossier');
      return;
    }
    
    updateStepStatus(dossierId, stepId, completed);
    toast.success(completed ? 'Étape marquée comme terminée' : 'Étape marquée comme non terminée');
  };

  const getStepIcon = (step) => {
    if (step.completed) return '✅';
    if (step.required) return '🔴';
    return '⚪';
  };

  const getStatusColor = (status) => {
    const colors = {
      'brouillon': 'text-gray-600 bg-gray-100',
      'en_cours': 'text-blue-600 bg-blue-100',
      'termine': 'text-green-600 bg-green-100',
      'annule': 'text-red-600 bg-red-100',
      'archive': 'text-gray-600 bg-gray-200'
    };
    return colors[status] || 'text-gray-600 bg-gray-100';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Non défini';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const completedSteps = dossier.steps?.filter(s => s.completed).length || 0;
  const totalSteps = dossier.steps?.length || 0;

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{dossier.numero}</h1>
              <p className="text-gray-600">{dossier.description}</p>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(dossier.status)}`}>
                {dossier.status.replace('_', ' ')}
              </span>
              {onClose && (
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <i className="fas fa-times text-xl"></i>
                </button>
              )}
            </div>
          </div>

          {/* Progress bar */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Progression</span>
              <span className="text-sm text-gray-500">{completedSteps}/{totalSteps} étapes</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-blue-600 h-3 rounded-full transition-all duration-300" 
                style={{ width: `${dossier.progress}%` }}
              ></div>
            </div>
            <div className="text-right mt-1">
              <span className="text-sm font-medium text-blue-600">{dossier.progress}%</span>
            </div>
          </div>

          {/* Info grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Client:</span>
              <span className="ml-2">{dossier.clientName}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <span className="ml-2 capitalize">{dossier.type}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Priorité:</span>
              <span className="ml-2 capitalize">{dossier.priority}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Créé le:</span>
              <span className="ml-2">{formatDate(dossier.createdAt)}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Modifié le:</span>
              <span className="ml-2">{formatDate(dossier.updatedAt)}</span>
            </div>
            {dossier.deadline && (
              <div>
                <span className="font-medium text-gray-700">Échéance:</span>
                <span className="ml-2">{formatDate(dossier.deadline)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('steps')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'steps'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Étapes du workflow
            </button>
            <button
              onClick={() => setActiveTab('documents')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'documents'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Documents
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Historique
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'steps' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Étapes du workflow {dossier.type}
                </h3>
                <div className="text-sm text-gray-500">
                  {completedSteps} sur {totalSteps} étapes terminées
                </div>
              </div>
              
              <div className="space-y-3">
                {dossier.steps?.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center p-4 rounded-lg border ${
                      step.completed 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-white border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center flex-1">
                      <span className="text-2xl mr-3">{getStepIcon(step)}</span>
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-500 mr-3">
                            Étape {index + 1}
                          </span>
                          <h4 className={`font-medium ${step.completed ? 'text-green-800' : 'text-gray-900'}`}>
                            {step.name}
                          </h4>
                          {step.required && (
                            <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                              Obligatoire
                            </span>
                          )}
                        </div>
                        {step.notes && (
                          <p className="text-sm text-gray-600 mt-1">{step.notes}</p>
                        )}
                        {step.completedAt && (
                          <p className="text-xs text-gray-500 mt-1">
                            Terminé le {formatDate(step.completedAt)}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {hasPermission('edit_dossier') && (
                      <div className="ml-4">
                        <label className="flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={step.completed}
                            onChange={(e) => handleStepToggle(step.id, e.target.checked)}
                            className="sr-only"
                          />
                          <div className={`w-6 h-6 rounded border-2 flex items-center justify-center ${
                            step.completed 
                              ? 'bg-green-500 border-green-500 text-white' 
                              : 'border-gray-300 hover:border-gray-400'
                          }`}>
                            {step.completed && <i className="fas fa-check text-xs"></i>}
                          </div>
                        </label>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'documents' && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">📄</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Gestion documentaire</h3>
              <p className="text-gray-500">Cette fonctionnalité sera disponible prochainement.</p>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">📋</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Historique des modifications</h3>
              <p className="text-gray-500">Cette fonctionnalité sera disponible prochainement.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DossierDetail;
