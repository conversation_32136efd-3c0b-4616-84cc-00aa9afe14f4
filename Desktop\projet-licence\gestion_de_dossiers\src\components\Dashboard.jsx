import { useState } from 'react'
import useStore from '../store'
import useDossierStore from '../store/dossierStore'

const Dashboard = () => {
  const { currentUser } = useStore()
  const { dossiers } = useDossierStore()
  const [dateFilter, setDateFilter] = useState('today')

  // Calcul des statistiques en temps réel
  const getStats = () => {
    const activeDossiers = dossiers.filter(d => d.status === 'en_cours').length
    const completedDossiers = dossiers.filter(d => d.status === 'termine').length
    const totalClients = new Set(dossiers.map(d => d.clientName)).size
    const inTransit = dossiers.filter(d => d.type === 'transit' && d.status === 'en_cours').length

    return [
      {
        id: 1,
        label: 'Dossiers actifs',
        value: activeDossiers.toString(),
        icon: 'folder-open',
        trend: '+12%',
        color: 'blue'
      },
      {
        id: 2,
        label: 'Clients',
        value: totalClients.toString(),
        icon: 'users',
        trend: '+5%',
        color: 'green'
      },
      {
        id: 3,
        label: 'En transit',
        value: inTransit.toString(),
        icon: 'truck',
        trend: '-2%',
        color: 'yellow'
      },
      {
        id: 4,
        label: 'Terminés',
        value: completedDossiers.toString(),
        icon: 'check-circle',
        trend: '+8%',
        color: 'purple'
      }
    ]
  }

  const stats = getStats()

  const recentActivities = [
    {
      id: 1,
      type: 'file',
      title: 'Nouveau dossier créé',
      description: 'Dossier #12345 pour Client XYZ',
      time: 'Il y a 5 minutes',
      status: 'success'
    },
    {
      id: 2,
      type: 'client',
      title: 'Nouveau client ajouté',
      description: 'Entreprise ABC',
      time: 'Il y a 1 heure',
      status: 'info'
    },
    {
      id: 3,
      type: 'transit',
      title: 'Mise à jour du statut',
      description: 'Dossier #12340 en transit',
      time: 'Il y a 2 heures',
      status: 'warning'
    }
  ]

  return (
    <div className="page-container">
      {/* Header */}
      <div className="section-header">
        <div>
          <h1 className="section-title">Tableau de bord</h1>
          <p className="section-subtitle">
            Bienvenue, {currentUser?.name || 'Utilisateur'} ! Voici un aperçu de votre activité.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className={`btn btn-sm ${dateFilter === 'today' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setDateFilter('today')}
          >
            Aujourd'hui
          </button>
          <button
            className={`btn btn-sm ${dateFilter === 'week' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setDateFilter('week')}
          >
            Cette semaine
          </button>
          <button
            className={`btn btn-sm ${dateFilter === 'month' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setDateFilter('month')}
          >
            Ce mois
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="content-grid content-grid-4 mb-8">
        {stats.map(stat => (
          <div key={stat.id} className="card card-elevated hover:scale-105 transition-transform duration-200">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-3 rounded-lg bg-${stat.color}-100`}>
                    <i className={`fas fa-${stat.icon} text-${stat.color}-600 text-xl`}></i>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
                <div className={`text-sm font-medium ${
                  stat.trend.startsWith('+') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.trend}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="content-grid content-grid-2 gap-8">
        {/* Recent Activities */}
        <div className="card card-elevated">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Activités récentes</h3>
            <button className="btn btn-ghost btn-sm">
              <i className="fas fa-external-link-alt mr-2"></i>
              Voir tout
            </button>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                recentActivities.map(activity => (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className={`p-2 rounded-full ${
                      activity.status === 'success' ? 'bg-green-100 text-green-600' :
                      activity.status === 'warning' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-blue-100 text-blue-600'
                    }`}>
                      <i className={`fas fa-${activity.type} text-sm`}></i>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-sm text-gray-500">{activity.description}</p>
                      <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                    </div>
                    <span className={`badge badge-sm ${
                      activity.status === 'success' ? 'badge-success' :
                      activity.status === 'warning' ? 'badge-warning' :
                      'badge-info'
                    }`}>
                      {activity.status}
                    </span>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <i className="fas fa-clock text-4xl text-gray-300 mb-4"></i>
                  <p className="text-gray-500">Aucune activité récente</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card card-elevated">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Actions rapides</h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <button className="w-full btn btn-primary justify-start">
                <i className="fas fa-plus mr-3"></i>
                Créer un nouveau dossier
              </button>
              <button className="w-full btn btn-outline justify-start">
                <i className="fas fa-users mr-3"></i>
                Ajouter un client
              </button>
              <button className="w-full btn btn-outline justify-start">
                <i className="fas fa-file-invoice mr-3"></i>
                Générer une facture
              </button>
              <button className="w-full btn btn-outline justify-start">
                <i className="fas fa-chart-bar mr-3"></i>
                Voir les rapports
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Chart */}
      <div className="mt-8">
        <div className="card card-elevated">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Performance</h3>
            <div className="flex items-center space-x-2">
              <button className="btn btn-ghost btn-sm">Jour</button>
              <button className="btn btn-primary btn-sm">Semaine</button>
              <button className="btn btn-ghost btn-sm">Mois</button>
            </div>
          </div>
          <div className="card-body">
            <div className="text-center py-16">
              <i className="fas fa-chart-line text-6xl text-gray-300 mb-4"></i>
              <h4 className="text-lg font-medium text-gray-900 mb-2">Graphiques de performance</h4>
              <p className="text-gray-500">Les graphiques détaillés seront disponibles prochainement.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard 