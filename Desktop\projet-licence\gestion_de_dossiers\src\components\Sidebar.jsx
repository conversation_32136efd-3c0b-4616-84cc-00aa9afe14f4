import { useNavigate } from 'react-router-dom'

const Sidebar = ({ currentPage }) => {
  const navigate = useNavigate()

  const menuItems = [
    { id: 'dashboard', label: 'Tableau de bord', icon: 'fas fa-chart-line' },
    { id: 'clients', label: 'Clients', icon: 'fas fa-users' },
    { id: 'files', label: 'Dossiers', icon: 'fas fa-folder' },
   
    { id: 'billing', label: 'Facturation', icon: 'fas fa-file-invoice-dollar' },
    { id: 'archive', label: 'Archives', icon: 'fas fa-archive' },
    { id: 'settings', label: 'Paramètres', icon: 'fas fa-cog' }
  ]

  return (
    <aside className="app-sidebar">
      <nav className="sidebar-nav">
        {menuItems.map(item => (
          <button
            key={item.id}
            className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
            onClick={() => navigate(`/${item.id}`)}
          >
            <i className={item.icon}></i>
            <span>{item.label}</span>
          </button>
        ))}
      </nav>
      
      
    </aside>
  )
}

export default Sidebar 