import React from 'react';
import { Link } from 'react-router-dom';
import { FaTimes, FaChartBar, FaDatabase } from 'react-icons/fa';
import useStore from '../../store';
import useDossierStore from '../../store/dossierStore';

const Sidebar = ({ isOpen, onClose, navItems, currentPath }) => {
  const { currentUser } = useStore();
  const { dossiers } = useDossierStore();

  // Calcul des statistiques pour la sidebar
  const activeDossiers = dossiers.filter(d => d.status === 'en_cours').length;
  const totalStorage = 1000; // 1TB en GB
  const usedStorage = 750; // 750GB utilisés
  const storagePercentage = (usedStorage / totalStorage) * 100;

  return (
    <>
      {/* Overlay pour mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onClose}
        />
      )}

      <nav
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        aria-label="Navigation principale"
      >
        {/* Header de la sidebar */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <i className="fas fa-truck text-white text-sm"></i>
            </div>
            <span className="font-semibold text-gray-900">Logistics Pro</span>
          </div>
          <button
            className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            onClick={onClose}
            aria-label="Fermer le menu"
          >
            <FaTimes className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Navigation */}
        <div className="flex-1 px-4 py-6 space-y-1">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={() => window.innerWidth < 768 && onClose()}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                currentPath === item.path
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
              aria-current={currentPath === item.path ? 'page' : undefined}
            >
              {item.icon && (
                <i className={`${item.icon} w-5 h-5 mr-3 ${
                  currentPath === item.path ? 'text-blue-700' : 'text-gray-400'
                }`}></i>
              )}
              <span>{item.label}</span>
              {item.path === '/files' && activeDossiers > 0 && (
                <span className="ml-auto bg-blue-100 text-blue-600 text-xs font-medium px-2 py-0.5 rounded-full">
                  {activeDossiers}
                </span>
              )}
            </Link>
          ))}
        </div>

        {/* Footer avec informations utilisateur et stockage */}
        <div className="border-t border-gray-200 p-4 space-y-4">
          {/* Informations de stockage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <FaDatabase className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">Stockage</span>
              </div>
              <span className="text-gray-500">{Math.round(storagePercentage)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  storagePercentage > 90 ? 'bg-red-500' :
                  storagePercentage > 75 ? 'bg-yellow-500' :
                  'bg-blue-500'
                }`}
                style={{ width: `${storagePercentage}%` }}
              />
            </div>
            <p className="text-xs text-gray-500">
              {usedStorage} GB utilisés sur {totalStorage} GB
            </p>
          </div>

          {/* Statistiques rapides */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <FaChartBar className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">Aperçu rapide</span>
            </div>
            <div className="space-y-1 text-xs text-gray-600">
              <div className="flex justify-between">
                <span>Dossiers actifs</span>
                <span className="font-medium">{activeDossiers}</span>
              </div>
              <div className="flex justify-between">
                <span>Total dossiers</span>
                <span className="font-medium">{dossiers.length}</span>
              </div>
            </div>
          </div>

          {/* Informations utilisateur */}
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {currentUser?.avatar || currentUser?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {currentUser?.name || 'Utilisateur'}
              </p>
              <p className="text-xs text-gray-500 capitalize">
                {currentUser?.role || 'Utilisateur'}
              </p>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Sidebar;
