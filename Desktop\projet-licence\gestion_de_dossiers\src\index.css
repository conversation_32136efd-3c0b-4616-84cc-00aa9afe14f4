@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary: #64748b;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

body {
  @apply bg-gray-50 text-gray-900;
}

.app-layout {
  @apply min-h-screen flex flex-col;
}

.app-header {
  @apply bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between;
}

.header-left {
  @apply flex items-center gap-4;
}

.logo {
  @apply flex items-center gap-2 text-xl font-semibold text-primary;
}

.header-center {
  @apply flex-1 max-w-2xl mx-4;
}

.global-search {
  @apply relative;
}

.global-search input {
  @apply w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
}

.global-search i {
  @apply absolute left-3 top-1/2 -translate-y-1/2 text-gray-400;
}

.header-right {
  @apply flex items-center gap-4;
}

.notification-icon {
  @apply relative cursor-pointer;
}

.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center;
}

.user-menu {
  @apply flex items-center gap-3 cursor-pointer;
}

.user-avatar {
  @apply w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center;
}

.user-avatar img {
  @apply w-full h-full object-cover rounded-full;
}

.user-avatar.default {
  @apply bg-primary text-white flex items-center justify-center;
}

.user-info {
  @apply text-sm;
}

.user-name {
  @apply font-medium;
}

.user-role {
  @apply text-gray-500;
}

.app-sidebar {
  @apply w-64 bg-white border-r border-gray-200 flex-col hidden md:flex transition-transform duration-300;
}

.menu-burger {
  @apply md:hidden flex items-center cursor-pointer p-2 rounded-lg hover:bg-gray-100 transition-colors;
}

.app-sidebar.active {
  @apply flex fixed inset-0 z-40 w-64 bg-white border-r border-gray-200 flex-col shadow-lg;
}

.sidebar-nav {
  @apply flex-1 py-4;
}

.nav-item {
  @apply w-full px-4 py-2 flex items-center gap-3 text-gray-600 hover:bg-gray-50 hover:text-primary transition-colors;
}

.nav-item.active {
  @apply bg-primary/10 text-primary;
}

.nav-item i {
  @apply w-5;
}

.nav-item:focus {
  @apply outline-none ring-2 ring-primary ring-offset-2;
}

.sidebar-footer {
  @apply p-4 border-t border-gray-200;
}

.storage-info {
  @apply space-y-2;
}

.storage-header {
  @apply flex items-center justify-between text-sm;
}

.storage-bar {
  @apply h-2 bg-gray-200 rounded-full overflow-hidden;
}

.storage-progress {
  @apply h-full bg-primary;
}

.storage-details {
  @apply text-xs text-gray-500;
}

.main-content {
  @apply flex-1 p-4 sm:p-6;
}

.page-header {
  @apply flex items-center justify-between mb-6;
}

.header-actions {
  @apply flex items-center gap-3;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-outline {
  @apply border border-gray-300 text-gray-700 hover:bg-gray-50;
}

.btn-icon {
  @apply p-2 rounded-lg text-gray-600 hover:bg-gray-100;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.btn-text {
  @apply text-gray-600 hover:text-primary;
}

.card {
  @apply bg-white rounded-lg border border-gray-200;
}

.card-header {
  @apply p-4 border-b border-gray-200 flex items-center justify-between;
}

.card-content {
  @apply p-4;
}

.table-container {
  @apply overflow-x-auto;
}

.data-table {
  @apply w-full;
}

.data-table th {
  @apply px-4 py-3 text-left text-sm font-medium text-gray-500 bg-gray-50;
}

.data-table td {
  @apply px-4 py-3 text-sm text-gray-900 border-t border-gray-200;
}

.data-table tr {
  @apply hover:bg-gray-50 cursor-pointer;
}

.data-table tr.selected {
  @apply bg-blue-50;
}

.table-actions {
  @apply flex items-center gap-2;
}

.status-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-badge.payé {
  @apply bg-green-100 text-green-800;
}

.status-badge.en-attente {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge.en-retard {
  @apply bg-red-100 text-red-800;
}

.status-badge.archivé {
  @apply bg-gray-100 text-gray-800;
}

.details-panel {
  @apply w-80 bg-white border-l border-gray-200;
}

.details-header {
  @apply p-4 border-b border-gray-200 flex items-center justify-between;
}

.details-content {
  @apply p-4 space-y-4;
}

.detail-item {
  @apply space-y-1;
}

.detail-item label {
  @apply font-semibold text-gray-600 mr-2;
}

.detail-item span {
  @apply text-sm font-medium;
}

.details-actions {
  @apply flex gap-2 mt-4;
}

.toggle-switch {
  @apply relative inline-block w-12 h-6;
}

.toggle-switch input {
  @apply opacity-0 w-0 h-0;
}

.toggle-slider {
  @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 rounded-full transition-colors;
}

.toggle-slider:before {
  @apply absolute content-[''] h-4 w-4 left-1 bottom-1 bg-white rounded-full transition-transform;
}

input:checked + .toggle-slider {
  @apply bg-primary;
}

input:checked + .toggle-slider:before {
  @apply translate-x-6;
}

select {
  @apply w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
}

.notifications-dropdown {
  @apply absolute right-0 top-full mt-2 w-80 bg-white rounded-lg border border-gray-200 shadow-lg;
}

.notifications-header {
  @apply p-4 border-b border-gray-200 flex items-center justify-between;
}

.notifications-list {
  @apply max-h-96 overflow-y-auto;
}

.notification-item {
  @apply p-4 border-b border-gray-200 hover:bg-gray-50;
}

.notification-item.unread {
  @apply bg-blue-50;
}

.notification-content {
  @apply space-y-1;
}

.notification-title {
  @apply font-medium;
}

.notification-message {
  @apply text-sm text-gray-600;
}

.notification-meta {
  @apply flex items-center justify-between mt-2;
}

.notification-time {
  @apply text-xs text-gray-500;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 cursor-pointer;
}

/* Table générique */
.table {
  @apply w-full border-collapse;
}

/* Layouts principaux */
.transit, .files, .clients-page, .billing-page, .dashboard {
  @apply p-6;
}

.transit-header, .files-header, .dashboard-header {
  @apply flex items-center justify-between mb-6;
}

.transit-content, .files-content, .dashboard-grid, .clients-grid, .billing-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6;
}

.transit-list, .files-list, .clients-list, .billing-list {
  @apply bg-white rounded-lg border border-gray-200 p-4 shadow;
}

/* Harmonisation des statuts pour correspondre aux classes générées */
.status-badge.payé, .status-badge.paye {
  @apply bg-green-100 text-green-800;
}
.status-badge[lang="fr"], .status-badge.en-attente, .status-badge.en_attente {
  @apply bg-yellow-100 text-yellow-800;
}
.status-badge.en-retard, .status-badge.en_retard {
  @apply bg-red-100 text-red-800;
}
.status-badge.archivé, .status-badge.archive {
  @apply bg-gray-100 text-gray-800;
}
.status-badge.actif {
  @apply bg-green-100 text-green-800;
}
.status-badge.inactif {
  @apply bg-gray-200 text-gray-600;
}

/* Classes utilitaires personnalisées */
.page-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6;
}

.section-header {
  @apply flex items-center justify-between mb-6;
}

.section-title {
  @apply text-2xl font-bold text-gray-900;
}

.section-subtitle {
  @apply text-gray-600 mt-1;
}

.content-grid {
  @apply grid gap-6;
}

.content-grid-1 {
  @apply grid-cols-1;
}

.content-grid-2 {
  @apply grid-cols-1 lg:grid-cols-2;
}

.content-grid-3 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.content-grid-4 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-4;
}

/* Composants de formulaire améliorés */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-input:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
  resize: vertical;
}

.form-error {
  @apply text-sm text-red-600 mt-1;
}

.form-help {
  @apply text-sm text-gray-500 mt-1;
}

/* Boutons améliorés */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-outline {
  @apply bg-white border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
}

.btn-ghost {
  @apply bg-transparent border-transparent text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:ring-blue-500;
}

.btn-icon {
  @apply p-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:ring-blue-500;
}

/* Cards améliorées */
.card {
  @apply bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden;
}

.card-elevated {
  @apply shadow-md hover:shadow-lg transition-shadow duration-200;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
  @apply p-6;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Badges et statuts améliorés */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-sm {
  @apply px-2 py-0.5 text-xs;
}

.badge-lg {
  @apply px-3 py-1 text-sm;
}

.badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
  @apply bg-gray-100 text-gray-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* Tables améliorées */
.table-wrapper {
  @apply overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg;
}

.table {
  @apply min-w-full divide-y divide-gray-300;
}

.table-header {
  @apply bg-gray-50;
}

.table-header th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-body td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table-row {
  @apply hover:bg-gray-50 cursor-pointer transition-colors;
}

.table-row.selected {
  @apply bg-blue-50;
}

/* Animations et transitions personnalisées */
.fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Définition des keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

.loading-pulse {
  @apply animate-pulse bg-gray-200 rounded;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.loading-dots::after {
  content: '';
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots::before {
  content: '';
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loadingDots 1.4s infinite ease-in-out both;
  animation-delay: -0.16s;
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animations d'interaction */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive utilities */
.mobile-only {
  @apply block md:hidden;
}

.desktop-only {
  @apply hidden md:block;
}

.tablet-up {
  @apply hidden sm:block;
}

/* Focus states améliorés */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}