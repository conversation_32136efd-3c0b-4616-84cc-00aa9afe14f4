import { useState } from 'react'
import useStore from '../store'
import useDossierStore, { WORKFLOW_TYPES } from '../store/dossierStore'

const Transit = () => {
  const { searchQuery } = useStore()
  const { dossiers } = useDossierStore()
  const [selectedTransit, setSelectedTransit] = useState(null)
  const [statusFilter, setStatusFilter] = useState('all')

  // Filtrer les dossiers de type transit
  const transitDossiers = dossiers.filter(dossier => dossier.type === WORKFLOW_TYPES.TRANSIT)

  // Données de démonstration pour les transits
  const [transits, setTransits] = useState([
    {
      id: 1,
      reference: 'TRN-A21-302',
      client: 'Global Imports Ltd',
      status: 'En transit',
      departure: 'Shanghai',
      arrival: 'Le Havre',
      date: '2024-02-20',
      estimatedArrival: '2024-03-15'
    },
    {
      id: 2,
      reference: 'TRN-B22-103',
      client: 'Tech Solutions Inc',
      status: 'En attente',
      departure: 'Rotterdam',
      arrival: 'Marseille',
      date: '2024-02-19',
      estimatedArrival: '2024-02-25'
    },
    {
      id: 3,
      reference: 'TRN-C23-405',
      client: 'Logistics Pro',
      status: 'Arrivé',
      departure: 'Singapore',
      arrival: 'Dunkerque',
      date: '2024-02-18',
      estimatedArrival: '2024-02-20'
    }
  ])

  const filteredTransits = transits.filter(transit =>
    transit.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transit.client.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="page-container">
      {/* Header */}
      <div className="section-header">
        <div>
          <h1 className="section-title">Transit & Douanes</h1>
          <p className="section-subtitle">
            Gérez vos opérations de transit douanier et suivez vos envois
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">Tous les statuts</option>
            <option value="en-transit">En transit</option>
            <option value="en-attente">En attente</option>
            <option value="arrive">Arrivé</option>
          </select>
          <button className="btn btn-primary">
            <i className="fas fa-plus mr-2"></i>
            Nouveau transit
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="content-grid content-grid-4 mb-8">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100 mr-4">
                <i className="fas fa-ship text-blue-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">En transit</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredTransits.filter(t => t.status === 'En transit').length}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-100 mr-4">
                <i className="fas fa-clock text-yellow-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">En attente</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredTransits.filter(t => t.status === 'En attente').length}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100 mr-4">
                <i className="fas fa-check-circle text-green-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Arrivés</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredTransits.filter(t => t.status === 'Arrivé').length}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-100 mr-4">
                <i className="fas fa-globe text-purple-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{filteredTransits.length}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table des transits */}
      <div className="card card-elevated">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Liste des transits</h3>
          <div className="flex items-center space-x-2">
            <button className="btn btn-ghost btn-sm">
              <i className="fas fa-download mr-2"></i>
              Exporter
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>Référence</th>
                <th>Client</th>
                <th>Statut</th>
                <th>Départ</th>
                <th>Arrivée</th>
                <th>Date départ</th>
                <th>Arrivée estimée</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredTransits.length === 0 ? (
                <tr>
                  <td colSpan="8" className="text-center py-8">
                    <div className="text-gray-500">
                      <i className="fas fa-ship text-4xl mb-4"></i>
                      <p>Aucun transit trouvé</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredTransits.map(transit => (
                  <tr
                    key={transit.id}
                    className={`table-row ${selectedTransit?.id === transit.id ? 'selected' : ''}`}
                    onClick={() => setSelectedTransit(transit)}
                  >
                    <td className="font-medium">{transit.reference}</td>
                    <td>{transit.client}</td>
                    <td>
                      <span className={`badge ${
                        transit.status === 'En transit' ? 'badge-primary' :
                        transit.status === 'En attente' ? 'badge-warning' :
                        'badge-success'
                      }`}>
                        {transit.status}
                      </span>
                    </td>
                    <td>{transit.departure}</td>
                    <td>{transit.arrival}</td>
                    <td>{new Date(transit.date).toLocaleDateString('fr-FR')}</td>
                    <td>{new Date(transit.estimatedArrival).toLocaleDateString('fr-FR')}</td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <button className="btn-icon" title="Modifier">
                          <i className="fas fa-edit"></i>
                        </button>
                        <button className="btn-icon" title="Supprimer">
                          <i className="fas fa-trash"></i>
                        </button>
                        <button className="btn-icon" title="Suivre">
                          <i className="fas fa-map-marker-alt"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Panneau de détails */}
      {selectedTransit && (
        <div className="mt-8">
          <div className="card card-elevated">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Détails du transit</h3>
              <div className="flex items-center space-x-2">
                <button className="btn btn-ghost btn-sm">
                  <i className="fas fa-edit mr-2"></i>
                  Modifier
                </button>
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={() => setSelectedTransit(null)}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Référence</label>
                  <p className="text-sm text-gray-900 font-medium">{selectedTransit.reference}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Client</label>
                  <p className="text-sm text-gray-900">{selectedTransit.client}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Statut</label>
                  <span className={`badge ${
                    selectedTransit.status === 'En transit' ? 'badge-primary' :
                    selectedTransit.status === 'En attente' ? 'badge-warning' :
                    'badge-success'
                  }`}>
                    {selectedTransit.status}
                  </span>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Port de départ</label>
                  <p className="text-sm text-gray-900">{selectedTransit.departure}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Port d'arrivée</label>
                  <p className="text-sm text-gray-900">{selectedTransit.arrival}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Date de départ</label>
                  <p className="text-sm text-gray-900">{new Date(selectedTransit.date).toLocaleDateString('fr-FR')}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Arrivée estimée</label>
                  <p className="text-sm text-gray-900">{new Date(selectedTransit.estimatedArrival).toLocaleDateString('fr-FR')}</p>
                </div>
              </div>

              {/* Actions supplémentaires */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <button className="btn btn-primary">
                    <i className="fas fa-map-marker-alt mr-2"></i>
                    Suivre en temps réel
                  </button>
                  <button className="btn btn-outline">
                    <i className="fas fa-file-pdf mr-2"></i>
                    Générer rapport
                  </button>
                  <button className="btn btn-outline">
                    <i className="fas fa-envelope mr-2"></i>
                    Notifier client
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Transit