import { useState } from 'react'
import useBillingStore, { BILLING_STATUS } from '../store/billingStore'
import useClientStore from '../store/clientStore'
import useBusinessLogic from '../hooks/useBusinessLogic'

const Billing = () => {
  const { factures, getFacturesByStatus, getStats } = useBillingStore()
  const { getClientById } = useClientStore()
  const { markFacturePaid } = useBusinessLogic()

  const [selectedFacture, setSelectedFacture] = useState(null)
  const [statusFilter, setStatusFilter] = useState('all')

  // Filtrage des factures
  const filteredFactures = statusFilter === 'all'
    ? factures
    : getFacturesByStatus(statusFilter)

  // Statistiques
  const stats = getStats()

  const handleMarkAsPaid = async (factureId, dossierId) => {
    try {
      await markFacturePaid(factureId, dossierId)
      setSelectedFacture(null)
    } catch (error) {
      console.error('Erreur lors du paiement:', error)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XAF',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getStatusBadgeClass = (status) => {
    const statusClasses = {
      [BILLING_STATUS.DRAFT]: 'badge-secondary',
      [BILLING_STATUS.SENT]: 'badge-warning',
      [BILLING_STATUS.PAID]: 'badge-success',
      [BILLING_STATUS.OVERDUE]: 'badge-danger'
    }
    return statusClasses[status] || 'badge-secondary'
  }

  return (
    <div className="page-container">
      {/* Header */}
      <div className="section-header">
        <div>
          <h1 className="section-title">Facturation</h1>
          <p className="section-subtitle">
            Gérez vos factures et suivez vos paiements
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">Tous les statuts</option>
            <option value={BILLING_STATUS.DRAFT}>Brouillon</option>
            <option value={BILLING_STATUS.SENT}>Envoyée</option>
            <option value={BILLING_STATUS.PAID}>Payée</option>
            <option value={BILLING_STATUS.OVERDUE}>En retard</option>
          </select>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="content-grid content-grid-4 mb-8">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100 mr-4">
                <i className="fas fa-file-invoice text-blue-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total factures</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFactures}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100 mr-4">
                <i className="fas fa-check-circle text-green-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Factures payées</p>
                <p className="text-2xl font-bold text-gray-900">{stats.facturesPayees}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-100 mr-4">
                <i className="fas fa-clock text-yellow-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">En attente</p>
                <p className="text-2xl font-bold text-gray-900">{stats.enAttente}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-100 mr-4">
                <i className="fas fa-money-bill text-purple-600 text-xl"></i>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Chiffre d'affaires</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.chiffreAffaires)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table des factures */}
      <div className="card card-elevated">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Liste des factures</h3>
          <div className="flex items-center space-x-2">
            <button className="btn btn-ghost btn-sm">
              <i className="fas fa-download mr-2"></i>
              Exporter
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>Numéro</th>
                <th>Client</th>
                <th>Dossier</th>
                <th>Montant</th>
                <th>Statut</th>
                <th>Date création</th>
                <th>Échéance</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredFactures.length === 0 ? (
                <tr>
                  <td colSpan="8" className="text-center py-8">
                    <div className="text-gray-500">
                      <i className="fas fa-file-invoice text-4xl mb-4"></i>
                      <p>Aucune facture trouvée</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredFactures.map(facture => (
                  <tr
                    key={facture.id}
                    className={`table-row ${selectedFacture?.id === facture.id ? 'selected' : ''}`}
                    onClick={() => setSelectedFacture(facture)}
                  >
                    <td className="font-medium">{facture.numero}</td>
                    <td>{facture.clientName}</td>
                    <td className="text-sm text-gray-500">{facture.dossierId}</td>
                    <td className="font-medium">{formatCurrency(facture.total)}</td>
                    <td>
                      <span className={`badge ${getStatusBadgeClass(facture.status)}`}>
                        {facture.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td>{new Date(facture.dateCreation).toLocaleDateString('fr-FR')}</td>
                    <td>{new Date(facture.dateEcheance).toLocaleDateString('fr-FR')}</td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <button className="btn-icon" title="Voir détails">
                          <i className="fas fa-eye"></i>
                        </button>
                        {facture.status === BILLING_STATUS.SENT && (
                          <button
                            className="btn-icon text-green-600"
                            title="Marquer comme payée"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleMarkAsPaid(facture.id, facture.dossierId)
                            }}
                          >
                            <i className="fas fa-check"></i>
                          </button>
                        )}
                        <button className="btn-icon" title="Télécharger PDF">
                          <i className="fas fa-download"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Panneau de détails */}
      {selectedFacture && (
        <div className="mt-8">
          <div className="card card-elevated">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Détails de la facture</h3>
              <button
                className="btn btn-ghost btn-sm"
                onClick={() => setSelectedFacture(null)}
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Numéro de facture</label>
                  <p className="text-sm text-gray-900 font-medium">{selectedFacture.numero}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Client</label>
                  <p className="text-sm text-gray-900">{selectedFacture.clientName}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Statut</label>
                  <span className={`badge ${getStatusBadgeClass(selectedFacture.status)}`}>
                    {selectedFacture.status.replace('_', ' ')}
                  </span>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Montant total</label>
                  <p className="text-sm text-gray-900 font-medium">{formatCurrency(selectedFacture.total)}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Date de création</label>
                  <p className="text-sm text-gray-900">{new Date(selectedFacture.dateCreation).toLocaleDateString('fr-FR')}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">Date d'échéance</label>
                  <p className="text-sm text-gray-900">{new Date(selectedFacture.dateEcheance).toLocaleDateString('fr-FR')}</p>
                </div>
              </div>

              {/* Détail des lignes */}
              <div className="mt-6">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Détail de la facturation</h4>
                <div className="overflow-x-auto">
                  <table className="table">
                    <thead className="table-header">
                      <tr>
                        <th>Description</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody className="table-body">
                      {selectedFacture.lignes.map((ligne, index) => (
                        <tr key={index}>
                          <td>{ligne.description}</td>
                          <td>{ligne.quantite}</td>
                          <td>{formatCurrency(ligne.prixUnitaire)}</td>
                          <td className="font-medium">{formatCurrency(ligne.total)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Totaux */}
                <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Sous-total :</span>
                      <span>{formatCurrency(selectedFacture.sousTotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>TVA (19.25%) :</span>
                      <span>{formatCurrency(selectedFacture.tva)}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>Total :</span>
                      <span>{formatCurrency(selectedFacture.total)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <button className="btn btn-primary">
                    <i className="fas fa-download mr-2"></i>
                    Télécharger PDF
                  </button>
                  {selectedFacture.status === BILLING_STATUS.SENT && (
                    <button
                      className="btn btn-success"
                      onClick={() => handleMarkAsPaid(selectedFacture.id, selectedFacture.dossierId)}
                    >
                      <i className="fas fa-check mr-2"></i>
                      Marquer comme payée
                    </button>
                  )}
                  <button className="btn btn-outline">
                    <i className="fas fa-envelope mr-2"></i>
                    Renvoyer par email
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Billing